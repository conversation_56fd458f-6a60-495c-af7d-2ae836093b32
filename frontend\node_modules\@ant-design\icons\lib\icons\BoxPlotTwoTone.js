"use strict";

var _interopRequireWildcard = require("@babel/runtime/helpers/interopRequireWildcard").default;
var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault").default;
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _extends2 = _interopRequireDefault(require("@babel/runtime/helpers/extends"));
var React = _interopRequireWildcard(require("react"));
var _BoxPlotTwoTone = _interopRequireDefault(require("@ant-design/icons-svg/lib/asn/BoxPlotTwoTone"));
var _AntdIcon = _interopRequireDefault(require("../components/AntdIcon"));
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY

var BoxPlotTwoTone = function BoxPlotTwoTone(props, ref) {
  return /*#__PURE__*/React.createElement(_AntdIcon.default, (0, _extends2.default)({}, props, {
    ref: ref,
    icon: _BoxPlotTwoTone.default
  }));
};

/**![box-plot](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTI5NiAzNjhoODh2Mjg4aC04OHptMTUyIDBoMjgwdjI4OEg0NDh6IiBmaWxsPSIjZTZmNGZmIiAvPjxwYXRoIGQ9Ik05NTIgMjI0aC01MmMtNC40IDAtOCAzLjYtOCA4djI0OGgtOTJWMzA0YzAtNC40LTMuNi04LTgtOEgyMzJjLTQuNCAwLTggMy42LTggOHYxNzZoLTkyVjIzMmMwLTQuNC0zLjYtOC04LThINzJjLTQuNCAwLTggMy42LTggOHY1NjBjMCA0LjQgMy42IDggOCA4aDUyYzQuNCAwIDgtMy42IDgtOFY1NDhoOTJ2MTcyYzAgNC40IDMuNiA4IDggOGg1NjBjNC40IDAgOC0zLjYgOC04VjU0OGg5MnYyNDRjMCA0LjQgMy42IDggOCA4aDUyYzQuNCAwIDgtMy42IDgtOFYyMzJjMC00LjQtMy42LTgtOC04ek0zODQgNjU2aC04OFYzNjhoODh2Mjg4em0zNDQgMEg0NDhWMzY4aDI4MHYyODh6IiBmaWxsPSIjMTY3N2ZmIiAvPjwvc3ZnPg==) */
var RefIcon = /*#__PURE__*/React.forwardRef(BoxPlotTwoTone);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'BoxPlotTwoTone';
}
var _default = exports.default = RefIcon;