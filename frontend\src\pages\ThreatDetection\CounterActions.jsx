import React, { useState, useEffect } from 'react'
import {
  Card,
  Table,
  Button,
  Space,
  Tag,
  Typography,
  Progress,
  Tooltip,
  Descriptions,
  Timeline,
  Modal
} from 'antd'
import {
  ReloadOutlined,
  EyeOutlined,
  PlayCircleOutlined,
  StopOutlined,
  CheckCircleOutlined,
  ClockCircleOutlined,
  ExclamationCircleOutlined
} from '@ant-design/icons'
import dayjs from '../../utils/dayjs'

const { Title, Text } = Typography

const CounterActions = () => {
  const [actions, setActions] = useState([])
  const [loading, setLoading] = useState(false)
  const [detailModalVisible, setDetailModalVisible] = useState(false)
  const [selectedAction, setSelectedAction] = useState(null)

  useEffect(() => {
    fetchCounterActions()
  }, [])

  const fetchCounterActions = async () => {
    try {
      setLoading(true)
      
      // 模拟 API 调用
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      // 模拟数据
      const mockActions = [
        {
          id: 1,
          action_id: 'counter_001',
          threat_id: 'threat_001',
          action_type: 'cs_beacon_block',
          action_method: 'IP封禁 + 流量重定向',
          action_description: '对CobaltStrike Beacon进行自动化反制',
          status: 'success',
          progress: 100,
          created_at: dayjs().subtract(1, 'hour'),
          started_at: dayjs().subtract(1, 'hour'),
          completed_at: dayjs().subtract(30, 'minute'),
          attempts_count: 1,
          max_attempts: 3,
          target_info: {
            host: '*************',
            port: 4444,
            threat_type: 'CobaltStrike Beacon'
          },
          result_data: {
            blocked_ips: ['*************'],
            redirected_traffic: 'honeypot:8080',
            success_rate: '100%'
          }
        },
        {
          id: 2,
          action_id: 'counter_002',
          threat_id: 'threat_002',
          action_type: 'webshell_removal',
          action_method: '文件隔离 + 权限回收',
          action_description: '清除WebShell后门并修复权限',
          status: 'in_progress',
          progress: 75,
          created_at: dayjs().subtract(30, 'minute'),
          started_at: dayjs().subtract(25, 'minute'),
          attempts_count: 1,
          max_attempts: 3,
          target_info: {
            host: '*************',
            port: 80,
            threat_type: 'PHP WebShell',
            file_path: '/uploads/shell.php'
          },
          result_data: {
            quarantined_files: ['/uploads/shell.php'],
            permissions_fixed: ['www-data'],
            progress_status: '正在验证清理结果...'
          }
        },
        {
          id: 3,
          action_id: 'counter_003',
          threat_id: 'threat_003',
          action_type: 'ssh_protection',
          action_method: 'IP黑名单 + 访问限制',
          action_description: '防护SSH暴力破解攻击',
          status: 'failed',
          progress: 45,
          created_at: dayjs().subtract(2, 'hour'),
          started_at: dayjs().subtract(2, 'hour'),
          attempts_count: 3,
          max_attempts: 3,
          error_message: '无法连接到目标主机进行防护配置',
          target_info: {
            host: '*************',
            port: 22,
            threat_type: '暴力破解攻击'
          },
          result_data: {
            blocked_ips: ['*******', '*******'],
            failed_reason: '目标主机网络不可达'
          }
        }
      ]
      
      setActions(mockActions)
    } catch (error) {
      console.error('获取反制行动失败:', error)
    } finally {
      setLoading(false)
    }
  }

  const getStatusTag = (status) => {
    const statusMap = {
      pending: { color: 'default', text: '等待中', icon: <ClockCircleOutlined /> },
      in_progress: { color: 'processing', text: '执行中', icon: <PlayCircleOutlined /> },
      success: { color: 'success', text: '成功', icon: <CheckCircleOutlined /> },
      failed: { color: 'error', text: '失败', icon: <ExclamationCircleOutlined /> },
      skipped: { color: 'warning', text: '已跳过', icon: <StopOutlined /> }
    }
    const config = statusMap[status] || { color: 'default', text: status }
    return (
      <Tag color={config.color} icon={config.icon}>
        {config.text}
      </Tag>
    )
  }

  const getActionTypeText = (type) => {
    const typeMap = {
      cs_beacon_block: 'CS Beacon阻断',
      webshell_removal: 'WebShell清除',
      ssh_protection: 'SSH防护',
      malware_quarantine: '恶意软件隔离',
      traffic_redirect: '流量重定向',
      honeypot_deploy: '蜜罐部署'
    }
    return typeMap[type] || type
  }

  const showActionDetail = (action) => {
    setSelectedAction(action)
    setDetailModalVisible(true)
  }

  const getTimelineItems = (action) => {
    const items = [
      {
        color: 'blue',
        children: (
          <div>
            <Text strong>反制行动创建</Text>
            <div>
              <Text type="secondary">{action.created_at.format('YYYY-MM-DD HH:mm:ss')}</Text>
            </div>
          </div>
        )
      }
    ]

    if (action.started_at) {
      items.push({
        color: 'green',
        children: (
          <div>
            <Text strong>开始执行</Text>
            <div>
              <Text type="secondary">{action.started_at.format('YYYY-MM-DD HH:mm:ss')}</Text>
            </div>
          </div>
        )
      })
    }

    if (action.status === 'success' && action.completed_at) {
      items.push({
        color: 'green',
        children: (
          <div>
            <Text strong>执行成功</Text>
            <div>
              <Text type="secondary">{action.completed_at.format('YYYY-MM-DD HH:mm:ss')}</Text>
            </div>
          </div>
        )
      })
    } else if (action.status === 'failed') {
      items.push({
        color: 'red',
        children: (
          <div>
            <Text strong>执行失败</Text>
            <div>
              <Text type="secondary">{action.error_message}</Text>
            </div>
          </div>
        )
      })
    } else if (action.status === 'in_progress') {
      items.push({
        color: 'blue',
        children: (
          <div>
            <Text strong>正在执行</Text>
            <div>
              <Text type="secondary">进度: {action.progress}%</Text>
            </div>
          </div>
        )
      })
    }

    return items
  }

  const columns = [
    {
      title: '行动ID',
      dataIndex: 'action_id',
      key: 'action_id',
      width: 120,
      render: (text) => (
        <Tooltip title={text}>
          <span style={{ fontFamily: 'monospace' }}>
            {text.substring(0, 8)}...
          </span>
        </Tooltip>
      )
    },
    {
      title: '反制类型',
      dataIndex: 'action_type',
      key: 'action_type',
      width: 120,
      render: getActionTypeText
    },
    {
      title: '目标信息',
      key: 'target',
      width: 150,
      render: (_, record) => (
        <div>
          <Text>{record.target_info.host}</Text>
          <div>
            <Text type="secondary" style={{ fontSize: '12px' }}>
              {record.target_info.threat_type}
            </Text>
          </div>
        </div>
      )
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 100,
      render: getStatusTag
    },
    {
      title: '进度',
      key: 'progress',
      width: 120,
      render: (_, record) => (
        <Progress 
          percent={record.progress} 
          size="small"
          status={record.status === 'failed' ? 'exception' : 
                 record.status === 'success' ? 'success' : 'normal'}
        />
      )
    },
    {
      title: '尝试次数',
      key: 'attempts',
      width: 100,
      render: (_, record) => `${record.attempts_count}/${record.max_attempts}`
    },
    {
      title: '创建时间',
      dataIndex: 'created_at',
      key: 'created_at',
      width: 120,
      render: (time) => time.format('MM-DD HH:mm')
    },
    {
      title: '操作',
      key: 'actions',
      width: 100,
      render: (_, record) => (
        <Space size="small">
          <Tooltip title="查看详情">
            <Button 
              type="text" 
              size="small" 
              icon={<EyeOutlined />}
              onClick={() => showActionDetail(record)}
            />
          </Tooltip>
        </Space>
      )
    }
  ]

  return (
    <div className="fade-in">
      <div className="toolbar">
        <div className="toolbar-left">
          <Title level={3} style={{ margin: 0 }}>
            反制管理
          </Title>
        </div>
        <div className="toolbar-right">
          <Button 
            icon={<ReloadOutlined />}
            onClick={fetchCounterActions}
            loading={loading}
          >
            刷新
          </Button>
        </div>
      </div>

      <Card>
        <Table
          columns={columns}
          dataSource={actions}
          rowKey="id"
          loading={loading}
          pagination={{
            total: actions.length,
            pageSize: 10,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total) => `共 ${total} 条记录`
          }}
          className="data-table"
        />
      </Card>

      {/* 反制行动详情模态框 */}
      <Modal
        title="反制行动详情"
        open={detailModalVisible}
        onCancel={() => setDetailModalVisible(false)}
        footer={[
          <Button key="close" onClick={() => setDetailModalVisible(false)}>
            关闭
          </Button>
        ]}
        width={800}
      >
        {selectedAction && (
          <div>
            <Descriptions title="基本信息" size="small" column={2} style={{ marginBottom: 16 }}>
              <Descriptions.Item label="行动ID">
                {selectedAction.action_id}
              </Descriptions.Item>
              <Descriptions.Item label="威胁ID">
                {selectedAction.threat_id}
              </Descriptions.Item>
              <Descriptions.Item label="反制类型">
                {getActionTypeText(selectedAction.action_type)}
              </Descriptions.Item>
              <Descriptions.Item label="反制方法">
                {selectedAction.action_method}
              </Descriptions.Item>
              <Descriptions.Item label="目标主机">
                {selectedAction.target_info.host}:{selectedAction.target_info.port}
              </Descriptions.Item>
              <Descriptions.Item label="威胁类型">
                {selectedAction.target_info.threat_type}
              </Descriptions.Item>
              <Descriptions.Item label="当前状态">
                {getStatusTag(selectedAction.status)}
              </Descriptions.Item>
              <Descriptions.Item label="执行进度">
                {selectedAction.progress}%
              </Descriptions.Item>
            </Descriptions>

            <div style={{ marginBottom: 16 }}>
              <Title level={5}>行动描述</Title>
              <Text>{selectedAction.action_description}</Text>
            </div>

            <div style={{ marginBottom: 16 }}>
              <Title level={5}>执行结果</Title>
              <Descriptions size="small" column={1}>
                {Object.entries(selectedAction.result_data || {}).map(([key, value]) => (
                  <Descriptions.Item key={key} label={key}>
                    {Array.isArray(value) ? value.join(', ') : value}
                  </Descriptions.Item>
                ))}
              </Descriptions>
            </div>

            <div>
              <Title level={5}>执行时间线</Title>
              <Timeline items={getTimelineItems(selectedAction)} />
            </div>
          </div>
        )}
      </Modal>
    </div>
  )
}

export default CounterActions
