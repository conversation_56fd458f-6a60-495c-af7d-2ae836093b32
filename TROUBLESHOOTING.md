# FibOpenDoor 故障排除指南

## 前端启动问题

### 1. 图标导入错误

**错误信息**: `The requested module does not provide an export named 'ShieldOutlined'`

**解决方案**:
```bash
# 1. 清除 node_modules 和重新安装
cd frontend
rm -rf node_modules package-lock.json
npm install

# 2. 清除 Vite 缓存
npm run dev -- --force

# 3. 如果问题持续，使用测试页面验证基础环境
# 编辑 src/main.jsx，注释 App 导入，取消注释 TestApp 导入
```

**已修复的图标**:
- `ShieldOutlined` → `SafetyOutlined`
- `SecurityScanOutlined` → `ScanOutlined` 或 `AlertOutlined`

### 2. Day.js 时间格式化错误

**错误信息**: `item.timestamp.fromNow is not a function`

**解决方案**:
```bash
# 这个问题已经在代码中修复
# 如果仍然遇到问题，请清除缓存并重新启动
rm -rf frontend/node_modules/.vite
npm run dev -- --force
```

**说明**: 项目已经配置了全局的 dayjs 设置，包含 `relativeTime` 插件，支持 `fromNow()` 方法。

### 3. 依赖安装问题

**错误信息**: `npm ERR! peer dep missing`

**解决方案**:
```bash
# 使用 --legacy-peer-deps 标志
npm install --legacy-peer-deps

# 或者使用 yarn
npm install -g yarn
yarn install
```

### 4. 端口占用问题

**错误信息**: `Port 3000 is already in use`

**解决方案**:
```bash
# 方法1: 使用不同端口
npm run dev -- --port 3001

# 方法2: 杀死占用端口的进程 (Windows)
netstat -ano | findstr :3000
taskkill /PID <PID> /F

# 方法2: 杀死占用端口的进程 (Linux/Mac)
lsof -ti:3000 | xargs kill -9
```

## 后端启动问题

### 1. Python 依赖问题

**错误信息**: `ModuleNotFoundError: No module named 'fastapi'`

**解决方案**:
```bash
# 确保使用正确的 Python 版本
python --version  # 应该是 3.8+

# 安装依赖
cd backend
pip install -r requirements.txt

# 如果使用虚拟环境
python -m venv venv
# Windows
venv\Scripts\activate
# Linux/Mac
source venv/bin/activate
pip install -r requirements.txt
```

### 2. 数据库连接问题

**错误信息**: `Could not connect to database`

**解决方案**:
```bash
# 检查数据库目录权限
mkdir -p data/database
chmod 755 data/database

# 检查 SQLite 文件权限
ls -la data/database/

# 如果使用 PostgreSQL，检查连接配置
# 编辑 .env 文件中的 DATABASE_URL
```

### 3. 端口占用问题

**错误信息**: `Address already in use`

**解决方案**:
```bash
# 修改 .env 文件中的端口
PORT=8001

# 或者杀死占用端口的进程
# Windows
netstat -ano | findstr :8000
taskkill /PID <PID> /F

# Linux/Mac
lsof -ti:8000 | xargs kill -9
```

## 工具配置问题

### 1. Masscan 未找到

**错误信息**: `masscan: command not found`

**解决方案**:
```bash
# Windows: 下载 Masscan 二进制文件
# 1. 从 GitHub 下载 masscan-binaries
# 2. 解压到 tools/masscan/ 目录
# 3. 确保 masscan.exe 可执行

# Linux: 安装 Masscan
sudo apt-get update
sudo apt-get install masscan

# 或从源码编译
git clone https://github.com/robertdavidgraham/masscan
cd masscan
make
sudo make install

# 验证安装
masscan --version
```

### 2. Nmap 未找到

**解决方案**:
```bash
# Windows: 下载并安装 Nmap
# 从 https://nmap.org/download.html 下载安装包

# Linux
sudo apt-get install nmap

# Mac
brew install nmap

# 验证安装
nmap --version
```

### 3. 权限问题

**错误信息**: `Permission denied`

**解决方案**:
```bash
# Linux/Mac: 使用 sudo 运行需要特权的扫描
sudo python main.py

# 或者设置 capabilities (Linux)
sudo setcap cap_net_raw+ep /usr/bin/masscan
sudo setcap cap_net_raw+ep /usr/bin/nmap

# Windows: 以管理员身份运行
```

## 网络连接问题

### 1. API 请求失败

**错误信息**: `Network Error` 或 `CORS error`

**解决方案**:
```bash
# 1. 检查后端是否正在运行
curl http://localhost:8000/health

# 2. 检查前端代理配置
# 编辑 frontend/vite.config.js 中的 proxy 设置

# 3. 检查防火墙设置
# Windows: 允许 Python 和 Node.js 通过防火墙
# Linux: 检查 iptables 规则
```

### 2. 跨域问题

**解决方案**:
```python
# 在 backend/main.py 中添加 CORS 中间件
from fastapi.middleware.cors import CORSMiddleware

app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:3000"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)
```

## 性能问题

### 1. 前端加载慢

**解决方案**:
```bash
# 1. 清除缓存
rm -rf frontend/node_modules/.vite

# 2. 优化构建
npm run build
npm run preview

# 3. 检查网络连接
```

### 2. 后端响应慢

**解决方案**:
```bash
# 1. 检查数据库性能
# 2. 增加工作进程数量
gunicorn -w 4 -k uvicorn.workers.UvicornWorker main:app

# 3. 使用 Redis 缓存
```

## 开发环境问题

### 1. 热重载不工作

**解决方案**:
```bash
# 前端
# 检查 vite.config.js 中的 server 配置

# 后端
# 确保使用 --reload 标志
uvicorn main:app --reload --host 0.0.0.0 --port 8000
```

### 2. 环境变量不生效

**解决方案**:
```bash
# 1. 检查 .env 文件位置和格式
# 2. 重启服务
# 3. 检查变量名是否正确
```

## 测试和调试

### 1. 使用测试页面

如果遇到复杂的前端问题，可以使用简化的测试页面：

```javascript
// 编辑 frontend/src/main.jsx
import TestApp from './TestApp.jsx'

// 将 <App /> 替换为 <TestApp />
```

### 2. 检查日志

```bash
# 后端日志
tail -f data/logs/app.log

# 前端控制台
# 打开浏览器开发者工具查看控制台错误
```

### 3. API 测试

```bash
# 使用 curl 测试 API
curl -X GET http://localhost:8000/health
curl -X GET http://localhost:8000/info

# 使用浏览器访问 API 文档
# http://localhost:8000/docs
```

## 常见错误代码

| 错误代码 | 说明 | 解决方案 |
|---------|------|----------|
| EADDRINUSE | 端口被占用 | 更改端口或杀死占用进程 |
| ENOENT | 文件或目录不存在 | 检查路径和权限 |
| EACCES | 权限不足 | 使用 sudo 或修改权限 |
| MODULE_NOT_FOUND | 模块未找到 | 重新安装依赖 |
| CORS_ERROR | 跨域错误 | 配置 CORS 中间件 |

## 获取帮助

如果以上解决方案都无法解决问题：

1. **检查系统要求**: 确保满足最低系统要求
2. **查看完整错误信息**: 复制完整的错误堆栈
3. **检查网络连接**: 确保能访问外部资源
4. **重新安装**: 删除所有依赖重新安装
5. **提交 Issue**: 包含详细的错误信息和环境信息

## 联系支持

- 查看项目文档: `README.md` 和 `INSTALL.md`
- API 文档: http://localhost:8000/docs
- 系统健康检查: http://localhost:8000/health
