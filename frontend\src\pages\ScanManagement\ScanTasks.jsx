import React, { useState, useEffect } from 'react'
import {
  Card,
  Table,
  Button,
  Space,
  Tag,
  Modal,
  Form,
  Input,
  Select,
  message,
  Typography,
  Progress,
  Tooltip,
  Popconfirm
} from 'antd'
import {
  PlusOutlined,
  PlayCircleOutlined,
  StopOutlined,
  DeleteOutlined,
  EyeOutlined,
  ReloadOutlined
} from '@ant-design/icons'
import dayjs from '../../utils/dayjs'

const { Title } = Typography
const { Option } = Select

const ScanTasks = () => {
  const [tasks, setTasks] = useState([])
  const [loading, setLoading] = useState(false)
  const [createModalVisible, setCreateModalVisible] = useState(false)
  const [form] = Form.useForm()

  useEffect(() => {
    fetchTasks()
  }, [])

  const fetchTasks = async () => {
    try {
      setLoading(true)
      
      // 模拟 API 调用
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      // 模拟数据
      const mockTasks = [
        {
          id: 1,
          task_id: 'scan_001',
          target: '***********/24',
          scan_type: 'port_scan',
          status: 'running',
          ports: '1-65535',
          created_at: dayjs().subtract(2, 'hour'),
          started_at: dayjs().subtract(1, 'hour'),
          total_hosts: 254,
          scanned_hosts: 156,
          open_ports_count: 45
        },
        {
          id: 2,
          task_id: 'scan_002',
          target: '********',
          scan_type: 'service_scan',
          status: 'completed',
          ports: '80,443,8080-8090',
          created_at: dayjs().subtract(1, 'day'),
          started_at: dayjs().subtract(1, 'day'),
          completed_at: dayjs().subtract(23, 'hour'),
          total_hosts: 1,
          scanned_hosts: 1,
          open_ports_count: 3
        },
        {
          id: 3,
          task_id: 'scan_003',
          target: '**********/16',
          scan_type: 'full_scan',
          status: 'failed',
          ports: '1-1000',
          created_at: dayjs().subtract(2, 'day'),
          started_at: dayjs().subtract(2, 'day'),
          total_hosts: 65536,
          scanned_hosts: 1024,
          open_ports_count: 0,
          error_message: '网络超时'
        }
      ]
      
      setTasks(mockTasks)
    } catch (error) {
      message.error('获取扫描任务失败')
    } finally {
      setLoading(false)
    }
  }

  const handleCreateTask = async (values) => {
    try {
      // 模拟创建任务
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      message.success('扫描任务创建成功')
      setCreateModalVisible(false)
      form.resetFields()
      fetchTasks()
    } catch (error) {
      message.error('创建扫描任务失败')
    }
  }

  const handleCancelTask = async (taskId) => {
    try {
      // 模拟取消任务
      await new Promise(resolve => setTimeout(resolve, 500))
      
      message.success('任务已取消')
      fetchTasks()
    } catch (error) {
      message.error('取消任务失败')
    }
  }

  const getStatusTag = (status) => {
    const statusMap = {
      pending: { color: 'default', text: '等待中' },
      running: { color: 'processing', text: '运行中' },
      completed: { color: 'success', text: '已完成' },
      failed: { color: 'error', text: '失败' },
      cancelled: { color: 'warning', text: '已取消' }
    }
    const config = statusMap[status] || { color: 'default', text: status }
    return <Tag color={config.color}>{config.text}</Tag>
  }

  const getScanTypeText = (type) => {
    const typeMap = {
      port_scan: '端口扫描',
      service_scan: '服务扫描',
      vulnerability_scan: '漏洞扫描',
      full_scan: '全面扫描'
    }
    return typeMap[type] || type
  }

  const getProgress = (task) => {
    if (task.status === 'completed') return 100
    if (task.status === 'failed' || task.status === 'cancelled') return 0
    if (task.total_hosts === 0) return 0
    return Math.round((task.scanned_hosts / task.total_hosts) * 100)
  }

  const columns = [
    {
      title: '任务ID',
      dataIndex: 'task_id',
      key: 'task_id',
      width: 120,
      render: (text) => (
        <Tooltip title={text}>
          <span style={{ fontFamily: 'monospace' }}>
            {text.substring(0, 8)}...
          </span>
        </Tooltip>
      )
    },
    {
      title: '扫描目标',
      dataIndex: 'target',
      key: 'target',
      width: 150
    },
    {
      title: '扫描类型',
      dataIndex: 'scan_type',
      key: 'scan_type',
      width: 100,
      render: getScanTypeText
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 80,
      render: getStatusTag
    },
    {
      title: '进度',
      key: 'progress',
      width: 120,
      render: (_, record) => {
        const progress = getProgress(record)
        return (
          <Progress 
            percent={progress} 
            size="small"
            status={record.status === 'failed' ? 'exception' : 'normal'}
          />
        )
      }
    },
    {
      title: '主机数',
      key: 'hosts',
      width: 100,
      render: (_, record) => `${record.scanned_hosts}/${record.total_hosts}`
    },
    {
      title: '开放端口',
      dataIndex: 'open_ports_count',
      key: 'open_ports_count',
      width: 80
    },
    {
      title: '创建时间',
      dataIndex: 'created_at',
      key: 'created_at',
      width: 120,
      render: (time) => time.format('MM-DD HH:mm')
    },
    {
      title: '操作',
      key: 'actions',
      width: 150,
      render: (_, record) => (
        <Space size="small">
          <Tooltip title="查看详情">
            <Button 
              type="text" 
              size="small" 
              icon={<EyeOutlined />}
              onClick={() => {/* 查看详情 */}}
            />
          </Tooltip>
          
          {record.status === 'running' && (
            <Popconfirm
              title="确定要取消这个任务吗？"
              onConfirm={() => handleCancelTask(record.task_id)}
            >
              <Tooltip title="取消任务">
                <Button 
                  type="text" 
                  size="small" 
                  icon={<StopOutlined />}
                  danger
                />
              </Tooltip>
            </Popconfirm>
          )}
          
          {['completed', 'failed', 'cancelled'].includes(record.status) && (
            <Popconfirm
              title="确定要删除这个任务吗？"
              onConfirm={() => {/* 删除任务 */}}
            >
              <Tooltip title="删除任务">
                <Button 
                  type="text" 
                  size="small" 
                  icon={<DeleteOutlined />}
                  danger
                />
              </Tooltip>
            </Popconfirm>
          )}
        </Space>
      )
    }
  ]

  return (
    <div className="fade-in">
      <div className="toolbar">
        <div className="toolbar-left">
          <Title level={3} style={{ margin: 0 }}>
            扫描任务
          </Title>
        </div>
        <div className="toolbar-right">
          <Button 
            icon={<ReloadOutlined />}
            onClick={fetchTasks}
            loading={loading}
          >
            刷新
          </Button>
          <Button 
            type="primary" 
            icon={<PlusOutlined />}
            onClick={() => setCreateModalVisible(true)}
          >
            新建扫描
          </Button>
        </div>
      </div>

      <Card>
        <Table
          columns={columns}
          dataSource={tasks}
          rowKey="id"
          loading={loading}
          pagination={{
            total: tasks.length,
            pageSize: 10,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total) => `共 ${total} 条记录`
          }}
          className="data-table"
        />
      </Card>

      {/* 创建扫描任务模态框 */}
      <Modal
        title="创建扫描任务"
        open={createModalVisible}
        onCancel={() => {
          setCreateModalVisible(false)
          form.resetFields()
        }}
        footer={null}
        width={600}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleCreateTask}
        >
          <Form.Item
            label="扫描目标"
            name="target"
            rules={[{ required: true, message: '请输入扫描目标' }]}
            help="支持单个IP、IP段或域名，如：*********** 或 ***********/24"
          >
            <Input placeholder="***********/24" />
          </Form.Item>

          <Form.Item
            label="扫描类型"
            name="scan_type"
            rules={[{ required: true, message: '请选择扫描类型' }]}
            initialValue="port_scan"
          >
            <Select>
              <Option value="port_scan">端口扫描</Option>
              <Option value="service_scan">服务扫描</Option>
              <Option value="vulnerability_scan">漏洞扫描</Option>
              <Option value="full_scan">全面扫描</Option>
            </Select>
          </Form.Item>

          <Form.Item
            label="端口范围"
            name="ports"
            initialValue="1-65535"
            help="如：80,443,8080-8090 或 1-65535"
          >
            <Input placeholder="1-65535" />
          </Form.Item>

          <Form.Item style={{ marginBottom: 0, textAlign: 'right' }}>
            <Space>
              <Button onClick={() => {
                setCreateModalVisible(false)
                form.resetFields()
              }}>
                取消
              </Button>
              <Button type="primary" htmlType="submit">
                创建任务
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  )
}

export default ScanTasks
