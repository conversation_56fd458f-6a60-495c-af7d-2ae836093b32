import React, { useState, useEffect } from 'react'
import {
  Card,
  Table,
  Button,
  Space,
  Tag,
  Input,
  Select,
  Typography,
  Tooltip,
  Collapse,
  Descriptions,
  Badge
} from 'antd'
import {
  SearchOutlined,
  ReloadOutlined,
  EyeOutlined,
  DownloadOutlined,
  FilterOutlined
} from '@ant-design/icons'
import dayjs from '../../utils/dayjs'

const { Title, Text } = Typography
const { Option } = Select
const { Panel } = Collapse

const ScanResults = () => {
  const [results, setResults] = useState([])
  const [loading, setLoading] = useState(false)
  const [searchText, setSearchText] = useState('')
  const [statusFilter, setStatusFilter] = useState('all')

  useEffect(() => {
    fetchResults()
  }, [])

  const fetchResults = async () => {
    try {
      setLoading(true)
      
      // 模拟 API 调用
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      // 模拟数据
      const mockResults = [
        {
          id: 1,
          task_id: 'scan_001',
          host: '*************',
          hostname: 'server01.local',
          is_alive: true,
          scan_time: dayjs().subtract(1, 'hour'),
          response_time: 45,
          ports: [
            { port: 22, protocol: 'tcp', state: 'open', service_name: 'ssh', service_version: 'OpenSSH 8.0' },
            { port: 80, protocol: 'tcp', state: 'open', service_name: 'http', service_version: 'Apache 2.4.41' },
            { port: 443, protocol: 'tcp', state: 'open', service_name: 'https', service_version: 'Apache 2.4.41' },
            { port: 3306, protocol: 'tcp', state: 'open', service_name: 'mysql', service_version: 'MySQL 8.0.25' }
          ],
          os_info: { name: 'Linux', version: 'Ubuntu 20.04' }
        },
        {
          id: 2,
          task_id: 'scan_001',
          host: '*************',
          hostname: null,
          is_alive: true,
          scan_time: dayjs().subtract(1, 'hour'),
          response_time: 23,
          ports: [
            { port: 80, protocol: 'tcp', state: 'open', service_name: 'http', service_version: 'nginx 1.18.0' },
            { port: 8080, protocol: 'tcp', state: 'open', service_name: 'http-proxy', service_version: 'Tomcat 9.0' }
          ],
          os_info: { name: 'Windows', version: 'Windows Server 2019' }
        },
        {
          id: 3,
          task_id: 'scan_002',
          host: '********',
          hostname: 'gateway.local',
          is_alive: true,
          scan_time: dayjs().subtract(1, 'day'),
          response_time: 12,
          ports: [
            { port: 80, protocol: 'tcp', state: 'open', service_name: 'http', service_version: 'lighttpd 1.4.55' },
            { port: 443, protocol: 'tcp', state: 'open', service_name: 'https', service_version: 'lighttpd 1.4.55' }
          ],
          os_info: { name: 'Linux', version: 'OpenWrt' }
        }
      ]
      
      setResults(mockResults)
    } catch (error) {
      console.error('获取扫描结果失败:', error)
    } finally {
      setLoading(false)
    }
  }

  const getServiceTag = (service) => {
    const serviceColors = {
      ssh: 'blue',
      http: 'green',
      https: 'green',
      mysql: 'orange',
      'http-proxy': 'purple'
    }
    return (
      <Tag color={serviceColors[service.service_name] || 'default'}>
        {service.port}/{service.protocol} - {service.service_name}
      </Tag>
    )
  }

  const expandedRowRender = (record) => {
    const portColumns = [
      {
        title: '端口',
        dataIndex: 'port',
        key: 'port',
        width: 80
      },
      {
        title: '协议',
        dataIndex: 'protocol',
        key: 'protocol',
        width: 80
      },
      {
        title: '状态',
        dataIndex: 'state',
        key: 'state',
        width: 80,
        render: (state) => (
          <Tag color={state === 'open' ? 'success' : 'default'}>
            {state}
          </Tag>
        )
      },
      {
        title: '服务',
        dataIndex: 'service_name',
        key: 'service_name',
        width: 120
      },
      {
        title: '版本',
        dataIndex: 'service_version',
        key: 'service_version',
        width: 200
      }
    ]

    return (
      <div style={{ padding: '16px 0' }}>
        <Descriptions title="主机详情" size="small" column={2} style={{ marginBottom: 16 }}>
          <Descriptions.Item label="操作系统">
            {record.os_info ? `${record.os_info.name} ${record.os_info.version}` : '未知'}
          </Descriptions.Item>
          <Descriptions.Item label="响应时间">
            {record.response_time}ms
          </Descriptions.Item>
          <Descriptions.Item label="扫描时间">
            {record.scan_time.format('YYYY-MM-DD HH:mm:ss')}
          </Descriptions.Item>
          <Descriptions.Item label="开放端口数">
            {record.ports.filter(p => p.state === 'open').length}
          </Descriptions.Item>
        </Descriptions>

        <Title level={5}>端口详情</Title>
        <Table
          columns={portColumns}
          dataSource={record.ports}
          rowKey="port"
          size="small"
          pagination={false}
        />
      </div>
    )
  }

  const columns = [
    {
      title: '主机地址',
      dataIndex: 'host',
      key: 'host',
      width: 140,
      render: (host, record) => (
        <div>
          <Text strong>{host}</Text>
          {record.hostname && (
            <div>
              <Text type="secondary" style={{ fontSize: '12px' }}>
                {record.hostname}
              </Text>
            </div>
          )}
        </div>
      )
    },
    {
      title: '状态',
      dataIndex: 'is_alive',
      key: 'is_alive',
      width: 80,
      render: (isAlive) => (
        <Badge 
          status={isAlive ? 'success' : 'default'} 
          text={isAlive ? '在线' : '离线'} 
        />
      )
    },
    {
      title: '开放端口',
      key: 'open_ports',
      width: 300,
      render: (_, record) => {
        const openPorts = record.ports.filter(p => p.state === 'open')
        return (
          <div>
            {openPorts.slice(0, 3).map(port => getServiceTag(port))}
            {openPorts.length > 3 && (
              <Tag>+{openPorts.length - 3} 更多</Tag>
            )}
          </div>
        )
      }
    },
    {
      title: '操作系统',
      key: 'os',
      width: 150,
      render: (_, record) => (
        record.os_info ? (
          <div>
            <Text>{record.os_info.name}</Text>
            <div>
              <Text type="secondary" style={{ fontSize: '12px' }}>
                {record.os_info.version}
              </Text>
            </div>
          </div>
        ) : (
          <Text type="secondary">未知</Text>
        )
      )
    },
    {
      title: '响应时间',
      dataIndex: 'response_time',
      key: 'response_time',
      width: 100,
      render: (time) => `${time}ms`
    },
    {
      title: '扫描时间',
      dataIndex: 'scan_time',
      key: 'scan_time',
      width: 120,
      render: (time) => time.format('MM-DD HH:mm')
    },
    {
      title: '操作',
      key: 'actions',
      width: 100,
      render: (_, record) => (
        <Space size="small">
          <Tooltip title="导出报告">
            <Button 
              type="text" 
              size="small" 
              icon={<DownloadOutlined />}
              onClick={() => {/* 导出报告 */}}
            />
          </Tooltip>
        </Space>
      )
    }
  ]

  const filteredResults = results.filter(result => {
    const matchesSearch = !searchText || 
      result.host.includes(searchText) || 
      (result.hostname && result.hostname.includes(searchText))
    
    const matchesStatus = statusFilter === 'all' || 
      (statusFilter === 'online' && result.is_alive) ||
      (statusFilter === 'offline' && !result.is_alive)
    
    return matchesSearch && matchesStatus
  })

  return (
    <div className="fade-in">
      <div className="toolbar">
        <div className="toolbar-left">
          <Title level={3} style={{ margin: 0 }}>
            扫描结果
          </Title>
        </div>
        <div className="toolbar-right">
          <Space>
            <Input
              placeholder="搜索主机地址或主机名"
              prefix={<SearchOutlined />}
              value={searchText}
              onChange={(e) => setSearchText(e.target.value)}
              style={{ width: 200 }}
            />
            <Select
              value={statusFilter}
              onChange={setStatusFilter}
              style={{ width: 120 }}
            >
              <Option value="all">全部状态</Option>
              <Option value="online">在线</Option>
              <Option value="offline">离线</Option>
            </Select>
            <Button 
              icon={<ReloadOutlined />}
              onClick={fetchResults}
              loading={loading}
            >
              刷新
            </Button>
          </Space>
        </div>
      </div>

      <Card>
        <Table
          columns={columns}
          dataSource={filteredResults}
          rowKey="id"
          loading={loading}
          expandable={{
            expandedRowRender,
            expandIcon: ({ expanded, onExpand, record }) => (
              <Button
                type="text"
                size="small"
                icon={<EyeOutlined />}
                onClick={e => onExpand(record, e)}
              >
                {expanded ? '收起' : '详情'}
              </Button>
            )
          }}
          pagination={{
            total: filteredResults.length,
            pageSize: 10,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total) => `共 ${total} 条记录`
          }}
          className="data-table"
        />
      </Card>
    </div>
  )
}

export default ScanResults
