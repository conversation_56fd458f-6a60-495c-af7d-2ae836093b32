# FibOpenDoor API 文档

## 概述

FibOpenDoor 提供完整的 RESTful API，支持所有核心功能的程序化访问。

**基础信息:**
- 基础URL: `http://localhost:8000/api/v1`
- 认证方式: <PERSON><PERSON> (可选)
- 数据格式: JSON
- 字符编码: UTF-8

## 认证

目前系统支持无认证访问，生产环境建议启用认证。

```http
Authorization: Bearer <your-token>
```

## 通用响应格式

### 成功响应
```json
{
  "success": true,
  "data": {},
  "message": "操作成功"
}
```

### 错误响应
```json
{
  "success": false,
  "error": "错误类型",
  "message": "错误描述",
  "details": {}
}
```

## 扫描管理 API

### 创建扫描任务

**POST** `/scan/tasks`

```json
{
  "target": "***********/24",
  "scan_type": "port_scan",
  "ports": "1-65535",
  "scan_options": {
    "rate": 1000,
    "timeout": 3600
  }
}
```

**响应:**
```json
{
  "success": true,
  "message": "扫描任务创建成功",
  "task_id": "scan_12345",
  "status": "pending"
}
```

### 获取扫描任务列表

**GET** `/scan/tasks?page=1&size=20&status=running`

**响应:**
```json
{
  "tasks": [
    {
      "id": 1,
      "task_id": "scan_12345",
      "target": "***********/24",
      "scan_type": "port_scan",
      "status": "running",
      "progress": 65,
      "created_at": "2025-07-08T14:30:00Z"
    }
  ],
  "total": 100,
  "page": 1,
  "size": 20
}
```

### 获取扫描任务详情

**GET** `/scan/tasks/{task_id}`

### 取消扫描任务

**DELETE** `/scan/tasks/{task_id}`

## 威胁检测 API

### 获取威胁列表

**GET** `/threat/threats?page=1&size=20&threat_type=cobalt_strike&threat_level=high`

**响应:**
```json
[
  {
    "id": 1,
    "threat_id": "threat_001",
    "target_host": "*************",
    "target_port": 4444,
    "threat_type": "cobalt_strike",
    "threat_level": "critical",
    "threat_name": "CobaltStrike Beacon",
    "confidence_score": 95,
    "is_active": true,
    "first_seen": "2025-07-08T12:00:00Z",
    "last_seen": "2025-07-08T14:25:00Z"
  }
]
```

### 获取威胁详情

**GET** `/threat/threats/{threat_id}`

### 创建反制行动

**POST** `/threat/threats/{threat_id}/counter`

**响应:**
```json
{
  "success": true,
  "message": "反制行动创建成功",
  "action_id": "counter_001"
}
```

### 获取反制行动列表

**GET** `/threat/threats/{threat_id}/counter`

### 获取威胁统计

**GET** `/threat/stats`

**响应:**
```json
{
  "total_threats": 42,
  "active_threats": 8,
  "threat_by_type": {
    "cobalt_strike": 5,
    "webshell": 3,
    "malware": 2
  },
  "threat_by_level": {
    "critical": 2,
    "high": 6,
    "medium": 15,
    "low": 19
  }
}
```

## 蜜罐系统 API

### 创建蜜罐实例

**POST** `/honeypot/instances`

```json
{
  "name": "SSH蜜罐-01",
  "honeypot_type": "ssh_server",
  "bind_port": 2222,
  "description": "SSH蜜罐用于捕获暴力破解攻击",
  "service_config": {
    "banner": "OpenSSH_8.0",
    "max_connections": 100
  }
}
```

### 获取蜜罐实例列表

**GET** `/honeypot/instances?page=1&size=20&status=running`

### 启动蜜罐

**POST** `/honeypot/instances/{instance_id}/start`

### 停止蜜罐

**POST** `/honeypot/instances/{instance_id}/stop`

### 获取蜜罐日志

**GET** `/honeypot/instances/{instance_id}/logs?page=1&size=50`

**响应:**
```json
[
  {
    "id": 1,
    "log_id": "log_001",
    "instance_id": "honeypot_001",
    "attacker_ip": "*******",
    "attack_type": "brute_force",
    "attack_description": "SSH暴力破解尝试",
    "attack_success": false,
    "risk_score": 75,
    "timestamp": "2025-07-08T14:20:00Z"
  }
]
```

### 获取蜜罐统计

**GET** `/honeypot/stats`

## 漏洞扫描 API

### 创建漏洞扫描

**POST** `/vulnerability/scans`

```json
{
  "target_list": [
    "http://*************",
    "http://*************"
  ],
  "scan_tool": "xray",
  "scan_config": {
    "plugins": ["xss", "sql", "cmd"],
    "depth": 3
  }
}
```

### 获取漏洞扫描列表

**GET** `/vulnerability/scans?page=1&size=20&status=completed`

### 获取漏洞列表

**GET** `/vulnerability/vulnerabilities?page=1&size=20&severity=high`

**响应:**
```json
[
  {
    "id": 1,
    "vuln_id": "vuln_001",
    "title": "SQL注入漏洞",
    "cve_id": "CVE-2023-1234",
    "severity": "high",
    "cvss_score": 8.5,
    "target_host": "*************",
    "target_port": 80,
    "vuln_type": "SQL Injection",
    "status": "open",
    "confidence": 95,
    "discovered_at": "2025-07-08T12:00:00Z"
  }
]
```

### 获取漏洞详情

**GET** `/vulnerability/vulnerabilities/{vuln_id}`

## 仪表板 API

### 获取系统概览

**GET** `/dashboard/overview`

**响应:**
```json
{
  "scans": {
    "total": 156,
    "running": 3,
    "completed": 153
  },
  "threats": {
    "total": 42,
    "active": 8,
    "resolved": 34
  },
  "honeypots": {
    "total": 12,
    "running": 8,
    "stopped": 4
  },
  "vulnerabilities": {
    "total": 89,
    "critical": 5,
    "others": 84
  }
}
```

### 获取最近活动

**GET** `/dashboard/activity?limit=20`

### 获取威胁趋势

**GET** `/dashboard/charts/threat-trend?days=7`

### 获取攻击来源

**GET** `/dashboard/charts/attack-sources?limit=10`

### 获取系统状态

**GET** `/dashboard/system-status`

**响应:**
```json
{
  "cpu_usage": 45.2,
  "memory_usage": 62.8,
  "disk_usage": 38.1,
  "load_average": {
    "1min": 0.85,
    "5min": 0.92,
    "15min": 1.05
  }
}
```

## 系统信息 API

### 健康检查

**GET** `/health`

**响应:**
```json
{
  "status": "healthy",
  "version": "1.0.0",
  "timestamp": "2025-07-08T14:30:00Z"
}
```

### 系统信息

**GET** `/info`

**响应:**
```json
{
  "app_name": "FibOpenDoor 自动化溯源反制系统",
  "version": "1.0.0",
  "debug": false,
  "tools_status": {
    "masscan": "available",
    "nmap": "available",
    "xray": "missing",
    "fscan": "missing"
  },
  "config": {
    "max_concurrent_scans": 10,
    "scan_timeout": 3600,
    "honeypot_max_instances": 50,
    "counter_attack_enabled": true
  }
}
```

## 错误代码

| 状态码 | 说明 |
|--------|------|
| 200 | 请求成功 |
| 201 | 创建成功 |
| 400 | 请求参数错误 |
| 401 | 未授权 |
| 403 | 权限不足 |
| 404 | 资源不存在 |
| 409 | 资源冲突 |
| 422 | 数据验证失败 |
| 500 | 服务器内部错误 |

## 使用示例

### Python 示例

```python
import requests

# 创建扫描任务
response = requests.post(
    'http://localhost:8000/api/v1/scan/tasks',
    json={
        'target': '***********/24',
        'scan_type': 'port_scan',
        'ports': '1-1000'
    }
)

if response.status_code == 200:
    task_id = response.json()['task_id']
    print(f'扫描任务创建成功: {task_id}')
```

### JavaScript 示例

```javascript
// 获取威胁列表
fetch('http://localhost:8000/api/v1/threat/threats?page=1&size=10')
  .then(response => response.json())
  .then(data => {
    console.log('威胁列表:', data);
  })
  .catch(error => {
    console.error('请求失败:', error);
  });
```

### cURL 示例

```bash
# 创建蜜罐实例
curl -X POST http://localhost:8000/api/v1/honeypot/instances \
  -H "Content-Type: application/json" \
  -d '{
    "name": "SSH蜜罐-01",
    "honeypot_type": "ssh_server",
    "bind_port": 2222
  }'
```

## 限制和注意事项

1. **并发限制**: 同时运行的扫描任务数量有限制
2. **权限要求**: 某些操作需要管理员权限
3. **网络访问**: 确保目标网络可达
4. **资源消耗**: 大规模扫描会消耗较多系统资源
5. **合规性**: 仅在授权网络中使用

## 更多信息

- **交互式文档**: http://localhost:8000/docs
- **OpenAPI 规范**: http://localhost:8000/openapi.json
