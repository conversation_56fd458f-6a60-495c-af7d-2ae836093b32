"use strict";

var _interopRequireWildcard = require("@babel/runtime/helpers/interopRequireWildcard").default;
var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault").default;
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _extends2 = _interopRequireDefault(require("@babel/runtime/helpers/extends"));
var React = _interopRequireWildcard(require("react"));
var _BaiduOutlined = _interopRequireDefault(require("@ant-design/icons-svg/lib/asn/BaiduOutlined"));
var _AntdIcon = _interopRequireDefault(require("../components/AntdIcon"));
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY

var BaiduOutlined = function BaiduOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(_AntdIcon.default, (0, _extends2.default)({}, props, {
    ref: ref,
    icon: _BaiduOutlined.default
  }));
};

/**![baidu](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIGZpbGwtcnVsZT0iZXZlbm9kZCIgdmlld0JveD0iNjQgNjQgODk2IDg5NiIgZm9jdXNhYmxlPSJmYWxzZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cGF0aCBkPSJNMjUwLjAyIDU0Ny4wNGM5Mi4zNy0xOS44IDc5Ljc3LTEzMC4wNyA3Ni45NS0xNTQuMTgtNC41Ni0zNy4yLTQ4LjI2LTEwMi4xNi0xMDcuNjMtOTcuMDItNzQuNyA2LjctODUuNjUgMTE0LjU4LTg1LjY1IDExNC41OC0xMC4wNCA0OS44OCAyNC4yIDE1Ni40MyAxMTYuMzMgMTM2LjYybTg0LjcgMjE0LjE0YzEwLjI4IDM4LjcgNDMuOTUgNDAuNDMgNDMuOTUgNDAuNDNINDI3VjY4My41NWgtNTEuNzRjLTIzLjIyIDYuOTYtMzQuNSAyNS4xLTM2Ljk4IDMyLjgtMi43NCA3LjgtOC43MSAyNy42LTMuNTcgNDQuODNtMTY5LjA3LTUzMS4xYzAtNzIuNDItNDEuMTMtMTMxLjA4LTkyLjItMTMxLjA4LTUwLjkyIDAtOTIuMjEgNTguNjYtOTIuMjEgMTMxLjA3IDAgNzIuNSA0MS4zIDEzMS4xNiA5Mi4yIDEzMS4xNiA1MS4wOCAwIDkyLjIxLTU4LjY2IDkyLjIxLTEzMS4xNm0yNDguMSA5LjFjOC44Ni01NC45Mi0zNS4wOC0xMTguODgtODMuMzQtMTI5LjgyLTQ4LjM0LTExLjEtMTA4LjcgNjYuMjgtMTE0LjE4IDExNi43NC02LjU1IDYxLjcyIDguNzkgMTIzLjI4IDc2Ljg2IDEzMi4wNiA2OC4xNiA4Ljg3IDExMi4wMy02My44NyAxMjAuNjUtMTE4Ljk3bTQ2LjM1IDQzMy4wMnMtMTA1LjQ3LTgxLjUzLTE2Ny0xNjkuNmMtODMuNC0xMjkuOTEtMjAxLjk4LTc3LjA1LTI0MS42Mi0xMS4wMi0zOS40NyA2Ni4wMy0xMDEgMTA3Ljg3LTEwOS43IDExOC45LTguODcgMTAuOTMtMTI3LjM2IDc0LjgtMTAxLjA3IDE5MS41NSAyNi4yOCAxMTYuNjUgMTE4LjczIDExNC41IDExOC43MyAxMTQuNXM2OC4wOCA2LjcgMTQ3LjEtMTAuOTRDNTIzLjcgODg4LjAzIDU5MS43IDkxMCA1OTEuNyA5MTBzMTg0LjU3IDYxLjcyIDIzNS4wNy01Ny4xOGM1MC40MS0xMTguOTctMjguNTMtMTgwLjYxLTI4LjUzLTE4MC42MU0zNjIuNDIgODQ5LjE3Yy01MS44My0xMC4zNi03Mi40Ny00NS42NS03NS4xMy01MS43LTIuNTctNi4xMy0xNy4yNC0zNC41NS05LjQ1LTgyLjg1IDIyLjM5LTcyLjQxIDg2LjIzLTc3LjYzIDg2LjIzLTc3LjYzaDYzLjg1di03OC40Nmw1NC40LjgyLjA4IDI4OS44MnptMjA1LjM4LS44M2MtNTMuNTYtMTMuNzUtNTYuMDUtNTEuNzgtNTYuMDUtNTEuNzhWNjQzLjk1bDU2LjA1LS45MnYxMzcuMTJjMy40IDE0LjU5IDIxLjY1IDE3LjMyIDIxLjY1IDE3LjMyaDU2Ljg4VjY0My45NWg1OS42MnYyMDQuMzl6bTMyMy44NC0zOTcuNzJjMC0yNi4zNS0yMS44OS0xMDUuNzItMTAzLjE1LTEwNS43Mi04MS40MyAwLTkyLjI5IDc0LjktOTIuMjkgMTI3Ljg0IDAgNTAuNTQgNC4zMSAxMjEuMTMgMTA1LjQgMTE4LjggMTAxLjE1LTIuMTUgOTAuMDQtMTE0LjQxIDkwLjA0LTE0MC45MiIgLz48L3N2Zz4=) */
var RefIcon = /*#__PURE__*/React.forwardRef(BaiduOutlined);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'BaiduOutlined';
}
var _default = exports.default = RefIcon;