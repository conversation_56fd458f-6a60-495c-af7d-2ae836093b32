"use strict";

var _interopRequireWildcard = require("@babel/runtime/helpers/interopRequireWildcard").default;
var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault").default;
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _extends2 = _interopRequireDefault(require("@babel/runtime/helpers/extends"));
var React = _interopRequireWildcard(require("react"));
var _BulbFilled = _interopRequireDefault(require("@ant-design/icons-svg/lib/asn/BulbFilled"));
var _AntdIcon = _interopRequireDefault(require("../components/AntdIcon"));
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY

var BulbFilled = function BulbFilled(props, ref) {
  return /*#__PURE__*/React.createElement(_AntdIcon.default, (0, _extends2.default)({}, props, {
    ref: ref,
    icon: _BulbFilled.default
  }));
};

/**![bulb](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTM0OCA2NzYuMUMyNTAgNjE5LjQgMTg0IDUxMy40IDE4NCAzOTJjMC0xODEuMSAxNDYuOS0zMjggMzI4LTMyOHMzMjggMTQ2LjkgMzI4IDMyOGMwIDEyMS40LTY2IDIyNy40LTE2NCAyODQuMVY3OTJjMCAxNy43LTE0LjMgMzItMzIgMzJIMzgwYy0xNy43IDAtMzItMTQuMy0zMi0zMlY2NzYuMXpNMzkyIDg4OGgyNDBjNC40IDAgOCAzLjYgOCA4djMyYzAgMTcuNy0xNC4zIDMyLTMyIDMySDQxNmMtMTcuNyAwLTMyLTE0LjMtMzItMzJ2LTMyYzAtNC40IDMuNi04IDgtOHoiIC8+PC9zdmc+) */
var RefIcon = /*#__PURE__*/React.forwardRef(BulbFilled);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'BulbFilled';
}
var _default = exports.default = RefIcon;