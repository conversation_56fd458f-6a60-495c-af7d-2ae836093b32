import React, { useState, useEffect } from 'react'
import {
  Card,
  Table,
  Button,
  Space,
  Tag,
  Modal,
  Form,
  Input,
  Select,
  message,
  Typography,
  Progress,
  Tooltip,
  Popconfirm
} from 'antd'
import {
  PlusOutlined,
  PlayCircleOutlined,
  StopOutlined,
  DeleteOutlined,
  EyeOutlined,
  ReloadOutlined,
  DownloadOutlined
} from '@ant-design/icons'
import dayjs from '../../utils/dayjs'

const { Title } = Typography
const { Option } = Select
const { TextArea } = Input

const VulnScans = () => {
  const [scans, setScans] = useState([])
  const [loading, setLoading] = useState(false)
  const [createModalVisible, setCreateModalVisible] = useState(false)
  const [form] = Form.useForm()

  useEffect(() => {
    fetchScans()
  }, [])

  const fetchScans = async () => {
    try {
      setLoading(true)
      
      // 模拟 API 调用
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      // 模拟数据
      const mockScans = [
        {
          id: 1,
          scan_id: 'vuln_001',
          scan_tool: 'xray',
          status: 'running',
          progress: 65,
          total_targets: 10,
          scanned_targets: 6,
          total_vulnerabilities: 8,
          critical_count: 1,
          high_count: 2,
          medium_count: 3,
          low_count: 2,
          info_count: 0,
          started_at: dayjs().subtract(30, 'minute'),
          target_list: ['http://*************', 'http://*************']
        },
        {
          id: 2,
          scan_id: 'vuln_002',
          scan_tool: 'fscan',
          status: 'completed',
          progress: 100,
          total_targets: 5,
          scanned_targets: 5,
          total_vulnerabilities: 12,
          critical_count: 0,
          high_count: 3,
          medium_count: 6,
          low_count: 3,
          info_count: 0,
          started_at: dayjs().subtract(2, 'hour'),
          completed_at: dayjs().subtract(1, 'hour'),
          target_list: ['***********/24']
        },
        {
          id: 3,
          scan_id: 'vuln_003',
          scan_tool: 'xray',
          status: 'failed',
          progress: 25,
          total_targets: 8,
          scanned_targets: 2,
          total_vulnerabilities: 0,
          critical_count: 0,
          high_count: 0,
          medium_count: 0,
          low_count: 0,
          info_count: 0,
          started_at: dayjs().subtract(1, 'day'),
          target_list: ['http://example.com']
        }
      ]
      
      setScans(mockScans)
    } catch (error) {
      message.error('获取漏洞扫描失败')
    } finally {
      setLoading(false)
    }
  }

  const handleCreateScan = async (values) => {
    try {
      // 模拟创建扫描
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      message.success('漏洞扫描任务创建成功')
      setCreateModalVisible(false)
      form.resetFields()
      fetchScans()
    } catch (error) {
      message.error('创建漏洞扫描失败')
    }
  }

  const getStatusTag = (status) => {
    const statusMap = {
      pending: { color: 'default', text: '等待中' },
      running: { color: 'processing', text: '运行中' },
      completed: { color: 'success', text: '已完成' },
      failed: { color: 'error', text: '失败' },
      cancelled: { color: 'warning', text: '已取消' }
    }
    const config = statusMap[status] || { color: 'default', text: status }
    return <Tag color={config.color}>{config.text}</Tag>
  }

  const getToolTag = (tool) => {
    const toolMap = {
      xray: { color: 'blue', text: 'Xray' },
      fscan: { color: 'green', text: 'fscan' },
      nmap_scripts: { color: 'orange', text: 'Nmap Scripts' }
    }
    const config = toolMap[tool] || { color: 'default', text: tool }
    return <Tag color={config.color}>{config.text}</Tag>
  }

  const columns = [
    {
      title: '扫描ID',
      dataIndex: 'scan_id',
      key: 'scan_id',
      width: 120,
      render: (text) => (
        <Tooltip title={text}>
          <span style={{ fontFamily: 'monospace' }}>
            {text.substring(0, 8)}...
          </span>
        </Tooltip>
      )
    },
    {
      title: '扫描工具',
      dataIndex: 'scan_tool',
      key: 'scan_tool',
      width: 100,
      render: getToolTag
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 80,
      render: getStatusTag
    },
    {
      title: '进度',
      key: 'progress',
      width: 120,
      render: (_, record) => (
        <Progress 
          percent={record.progress} 
          size="small"
          status={record.status === 'failed' ? 'exception' : 'normal'}
        />
      )
    },
    {
      title: '目标数',
      key: 'targets',
      width: 100,
      render: (_, record) => `${record.scanned_targets}/${record.total_targets}`
    },
    {
      title: '漏洞统计',
      key: 'vulnerabilities',
      width: 200,
      render: (_, record) => (
        <Space size="small">
          {record.critical_count > 0 && (
            <Tag color="red">严重: {record.critical_count}</Tag>
          )}
          {record.high_count > 0 && (
            <Tag color="orange">高危: {record.high_count}</Tag>
          )}
          {record.medium_count > 0 && (
            <Tag color="yellow">中危: {record.medium_count}</Tag>
          )}
          {record.low_count > 0 && (
            <Tag color="blue">低危: {record.low_count}</Tag>
          )}
          {record.total_vulnerabilities === 0 && (
            <Tag color="green">无漏洞</Tag>
          )}
        </Space>
      )
    },
    {
      title: '开始时间',
      dataIndex: 'started_at',
      key: 'started_at',
      width: 120,
      render: (time) => time.format('MM-DD HH:mm')
    },
    {
      title: '操作',
      key: 'actions',
      width: 150,
      render: (_, record) => (
        <Space size="small">
          <Tooltip title="查看详情">
            <Button 
              type="text" 
              size="small" 
              icon={<EyeOutlined />}
              onClick={() => {/* 查看详情 */}}
            />
          </Tooltip>
          
          {record.status === 'completed' && (
            <Tooltip title="下载报告">
              <Button 
                type="text" 
                size="small" 
                icon={<DownloadOutlined />}
                onClick={() => {/* 下载报告 */}}
              />
            </Tooltip>
          )}
          
          {record.status === 'running' && (
            <Popconfirm
              title="确定要停止这个扫描吗？"
              onConfirm={() => {/* 停止扫描 */}}
            >
              <Tooltip title="停止扫描">
                <Button 
                  type="text" 
                  size="small" 
                  icon={<StopOutlined />}
                  danger
                />
              </Tooltip>
            </Popconfirm>
          )}
          
          {['completed', 'failed', 'cancelled'].includes(record.status) && (
            <Popconfirm
              title="确定要删除这个扫描吗？"
              onConfirm={() => {/* 删除扫描 */}}
            >
              <Tooltip title="删除扫描">
                <Button 
                  type="text" 
                  size="small" 
                  icon={<DeleteOutlined />}
                  danger
                />
              </Tooltip>
            </Popconfirm>
          )}
        </Space>
      )
    }
  ]

  return (
    <div className="fade-in">
      <div className="toolbar">
        <div className="toolbar-left">
          <Title level={3} style={{ margin: 0 }}>
            漏洞扫描
          </Title>
        </div>
        <div className="toolbar-right">
          <Button 
            icon={<ReloadOutlined />}
            onClick={fetchScans}
            loading={loading}
          >
            刷新
          </Button>
          <Button 
            type="primary" 
            icon={<PlusOutlined />}
            onClick={() => setCreateModalVisible(true)}
          >
            新建扫描
          </Button>
        </div>
      </div>

      <Card>
        <Table
          columns={columns}
          dataSource={scans}
          rowKey="id"
          loading={loading}
          pagination={{
            total: scans.length,
            pageSize: 10,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total) => `共 ${total} 条记录`
          }}
          className="data-table"
        />
      </Card>

      {/* 创建漏洞扫描模态框 */}
      <Modal
        title="创建漏洞扫描"
        open={createModalVisible}
        onCancel={() => {
          setCreateModalVisible(false)
          form.resetFields()
        }}
        footer={null}
        width={600}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleCreateScan}
        >
          <Form.Item
            label="扫描工具"
            name="scan_tool"
            rules={[{ required: true, message: '请选择扫描工具' }]}
            initialValue="xray"
          >
            <Select>
              <Option value="xray">Xray</Option>
              <Option value="fscan">fscan</Option>
              <Option value="nmap_scripts">Nmap Scripts</Option>
            </Select>
          </Form.Item>

          <Form.Item
            label="扫描目标"
            name="target_list"
            rules={[{ required: true, message: '请输入扫描目标' }]}
            help="每行一个目标，支持URL或IP地址"
          >
            <TextArea 
              rows={4}
              placeholder={`http://*************
http://*************
***********/24`}
            />
          </Form.Item>

          <Form.Item style={{ marginBottom: 0, textAlign: 'right' }}>
            <Space>
              <Button onClick={() => {
                setCreateModalVisible(false)
                form.resetFields()
              }}>
                取消
              </Button>
              <Button type="primary" htmlType="submit">
                开始扫描
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  )
}

export default VulnScans
